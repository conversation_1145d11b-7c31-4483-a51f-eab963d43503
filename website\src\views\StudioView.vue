<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面标题 -->
    <section class="py-12 bg-gradient-primary text-white">
      <div class="container mx-auto px-4 text-center">
          <h1 class="text-4xl md:text-5xl font-bold mb-4">承制厂牌</h1>
          <p class="text-xl max-w-3xl mx-auto opacity-90">
            专业短剧内容生产商，打造爆款短剧IP，提供全流程承制服务
          </p>
        </div>
      </section>

      <div class="container mx-auto px-4">
        <!-- 我们的优势 -->
      <section class="py-16">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gradient">我们的优势</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            专业制作团队，高效内容生产，确保项目品质
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- 优势卡片1 -->
          <div class="bg-white rounded-xl shadow-md p-6 transition-transform hover:-translate-y-1 hover:shadow-lg">
            <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <svg class="w-8 h-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">专业制作团队</h3>
            <p class="text-gray-600">
              由资深导演、编剧、摄影师组成的专业团队，拥有丰富的内容创作经验，确保作品质量
            </p>
          </div>
          
          <!-- 优势卡片2 -->
          <div class="bg-white rounded-xl shadow-md p-6 transition-transform hover:-translate-y-1 hover:shadow-lg">
            <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <svg class="w-8 h-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">创意内容策划</h3>
            <p class="text-gray-600">
              洞察市场趋势，把握用户喜好，打造符合平台规则和受众口味的爆款内容
            </p>
          </div>
          
          <!-- 优势卡片3 -->
          <div class="bg-white rounded-xl shadow-md p-6 transition-transform hover:-translate-y-1 hover:shadow-lg">
            <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <svg class="w-8 h-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">高效制作流程</h3>
            <p class="text-gray-600">
              标准化的制作流程，精细的项目管理，确保作品按时、高质量交付
            </p>
          </div>
        </div>
      </section>
      
      <!-- 服务内容 -->
      <section class="py-16 bg-white">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gradient">服务内容</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            为您提供专业的短剧内容制作服务
          </p>
        </div>
        
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
          <div class="grid grid-cols-1 md:grid-cols-2">
            <!-- 图片部分 -->
            <div class="bg-primary h-60 md:h-auto flex items-center justify-center">
              <svg class="w-32 h-32 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </div>
            
            <!-- 服务列表部分 -->
            <div class="p-8">
              <h3 class="text-xl font-bold mb-4">全流程短剧制作</h3>
              <ul class="space-y-3">
                <li class="flex items-start">
                  <svg class="w-5 h-5 text-primary mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span class="text-gray-700">IP选题与剧本创作</span>
                </li>
                <li class="flex items-start">
                  <svg class="w-5 h-5 text-primary mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span class="text-gray-700">演员选角与拍摄制作</span>
                </li>
                <li class="flex items-start">
                  <svg class="w-5 h-5 text-primary mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span class="text-gray-700">后期剪辑与特效制作</span>
                </li>
                <li class="flex items-start">
                  <svg class="w-5 h-5 text-primary mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span class="text-gray-700">平台发行与推广运营</span>
                </li>
                <li class="flex items-start">
                  <svg class="w-5 h-5 text-primary mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span class="text-gray-700">数据分析与收益分配</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>
      
      <!-- 合作流程 -->
      <section class="py-16">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gradient">合作流程</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            高效透明的合作方式，确保项目顺利进行
          </p>
        </div>
        
        <div class="relative">
          <!-- 时间轴 -->
          <div class="hidden md:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-gray-200 -translate-x-1/2"></div>
          
          <div class="space-y-12">
            <!-- 步骤1 -->
            <div class="flex flex-col md:flex-row">
              <div class="md:w-1/2 md:pr-8 text-right mb-4 md:mb-0">
                <h3 class="text-xl font-bold mb-2">需求沟通</h3>
                <p class="text-gray-600">了解投资方对项目的要求，确定内容方向和投资预算</p>
              </div>
              <div class="flex md:justify-center">
                <div class="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center z-10">1</div>
              </div>
              <div class="md:w-1/2 md:pl-8 md:pt-0 pt-4"></div>
            </div>
            
            <!-- 步骤2 -->
            <div class="flex flex-col md:flex-row">
              <div class="md:w-1/2 md:pr-8 text-right hidden md:block"></div>
              <div class="flex md:justify-center">
                <div class="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center z-10">2</div>
              </div>
              <div class="md:w-1/2 md:pl-8 pt-4 md:pt-0">
                <h3 class="text-xl font-bold mb-2">方案提交</h3>
                <p class="text-gray-600">根据需求提供创意方案和制作计划，包括剧本大纲、演员阵容、制作周期等</p>
              </div>
            </div>
            
            <!-- 步骤3 -->
            <div class="flex flex-col md:flex-row">
              <div class="md:w-1/2 md:pr-8 text-right mb-4 md:mb-0">
                <h3 class="text-xl font-bold mb-2">合约签订</h3>
                <p class="text-gray-600">确认合作细节，签订制作合约，明确双方权责和收益分配方式</p>
              </div>
              <div class="flex md:justify-center">
                <div class="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center z-10">3</div>
              </div>
              <div class="md:w-1/2 md:pl-8 md:pt-0 pt-4"></div>
            </div>
            
            <!-- 步骤4 -->
            <div class="flex flex-col md:flex-row">
              <div class="md:w-1/2 md:pr-8 text-right hidden md:block"></div>
              <div class="flex md:justify-center">
                <div class="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center z-10">4</div>
              </div>
              <div class="md:w-1/2 md:pl-8 pt-4 md:pt-0">
                <h3 class="text-xl font-bold mb-2">制作执行</h3>
                <p class="text-gray-600">按照计划进行拍摄制作，定期向投资方汇报进度，确保质量控制</p>
              </div>
            </div>
            
            <!-- 步骤5 -->
            <div class="flex flex-col md:flex-row">
              <div class="md:w-1/2 md:pr-8 text-right mb-4 md:mb-0">
                <h3 class="text-xl font-bold mb-2">发行变现</h3>
                <p class="text-gray-600">作品完成后进行平台发行，通过广告、付费等方式获取收益，并按约定分配</p>
              </div>
              <div class="flex md:justify-center">
                <div class="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center z-10">5</div>
              </div>
              <div class="md:w-1/2 md:pl-8 md:pt-0 pt-4"></div>
            </div>
          </div>
        </div>
      </section>
      
      <!-- 联系合作 -->
      <section class="py-16 bg-gradient-primary text-white">
        <div class="container mx-auto px-4 text-center">
          <h2 class="text-3xl font-bold mb-6">精品制作 · 内容共创</h2>
          <p class="text-xl mb-8 max-w-3xl mx-auto">
            如果您有短剧制作需求或投资意向，欢迎与我们联系
          </p>
          <div class="flex flex-col sm:flex-row justify-center gap-4">
            <button class="btn bg-white text-primary hover:bg-gray-100 px-8 py-3 rounded-lg transition-colors">
              项目合作
            </button>
            <button class="btn bg-transparent border-2 border-white hover:bg-white/10 px-8 py-3 rounded-lg transition-colors">
              商务咨询
            </button>
          </div>
        </div>
      </section>
      </div>
  </div>
</template>

<style scoped>
.bg-gradient-primary {
  background: linear-gradient(45deg, var(--color-purple-gradient-start) 50%, var(--color-purple-gradient-end) 100%);
}

.text-gradient {
  background: linear-gradient(to right, var(--color-purple-gradient-start), var(--color-purple-gradient-end));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}
</style> 