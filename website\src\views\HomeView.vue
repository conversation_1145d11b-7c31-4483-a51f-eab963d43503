<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import HomeBanner from '../components/HomeBanner.vue'
import DramaTag from '../components/common/DramaTag.vue'
import { getPublicDramas } from '../api/dramaService'
import { getWebsiteStats, type WebsiteStats } from '../api/websiteAPI'
import { useTags } from '../composables/useTags'
import { useRouter } from 'vue-router'
import type { Drama } from '../types'

const router = useRouter()

// 标签管理
const { loadTags, tagMap, parseTagData } = useTags()



// 工具提示当前活跃ID
const activeTooltip = ref(null)

// 显示工具提示
const showTooltip = (id) => {
  activeTooltip.value = id
}

// 隐藏工具提示
const hideTooltip = () => {
  activeTooltip.value = null
}

// 当前悬停的团队成员
const hoveredMember = ref(null)

// 设置悬停成员
const setHoveredMember = (member) => {
  hoveredMember.value = member
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 使用默认头像SVG
  img.style.display = 'none'
  const parent = img.parentElement
  if (parent) {
    parent.innerHTML = `
      <svg viewBox="0 0 24 24" class="w-12 h-12 text-white">
        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" fill="currentColor" />
      </svg>
    `
  }
}

// 清除悬停成员
const clearHoveredMember = () => {
  hoveredMember.value = null
}

// 数据状态
const dramas = ref<Drama[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// 网站统计数据
const websiteStats = ref<WebsiteStats | null>(null)
const statsLoading = ref(false)
const statsError = ref<string | null>(null)

// 实时投资人数（在1575-1685之间随机变化）
const realtimeInvestors = ref(1575)
const currentUserIndex = ref(0)

// 定时器引用
let investorTimer: NodeJS.Timeout | null = null
let userTimer: NodeJS.Timeout | null = null

const highlights = ref([
  {
    id: 1,
    title: "《都市情缘》",
    description: "现代都市爱情剧，投资回报率达18%",
    image: "https://via.placeholder.com/400x300/3B82F6/FFFFFF?text=都市情缘",
    roi: 18,
    icon: 'chart-pie',
    color: 'bg-blue-500'
  },
  {
    id: 2,
    title: "《青春校园》",
    description: "校园青春题材，深受年轻观众喜爱",
    image: "https://via.placeholder.com/400x300/10B981/FFFFFF?text=青春校园",
    roi: 22,
    icon: 'users',
    color: 'bg-green-500'
  },
  {
    id: 3,
    title: "《商战风云》",
    description: "商业题材短剧，市场表现优异",
    image: "https://via.placeholder.com/400x300/8B5CF6/FFFFFF?text=商战风云",
    roi: 16,
    icon: 'trending-up',
    color: 'bg-purple-500'
  }
])

const teamMembers = ref([
  {
    id: 1,
    name: "张总监",
    position: "投资总监",
    avatar: "https://via.placeholder.com/150x150/8667F0/FFFFFF?text=张",
    experience: "10年影视投资经验"
  },
  {
    id: 2,
    name: "李制片",
    position: "制片人",
    avatar: "https://via.placeholder.com/150x150/6039E4/FFFFFF?text=李",
    experience: "资深制片人，制作过多部热门短剧"
  },
  {
    id: 3,
    name: "王分析师",
    position: "市场分析师",
    avatar: "https://via.placeholder.com/150x150/8667F0/FFFFFF?text=王",
    experience: "专业市场分析，精准把握投资机会"
  },
  {
    id: 4,
    name: "赵经理",
    position: "项目经理",
    avatar: "https://via.placeholder.com/150x150/6039E4/FFFFFF?text=赵",
    experience: "丰富的项目管理经验"
  }
])

const investmentProcess = ref([
  {
    id: 1,
    title: "注册账户",
    description: "快速注册投资账户",
    details: "完成实名认证，开通投资账户，享受专业投资服务",
    icon: "user-plus",
    color: "bg-blue-500"
  },
  {
    id: 2,
    title: "选择项目",
    description: "浏览优质短剧项目",
    details: "专业团队精选优质短剧项目，详细的项目分析报告",
    icon: "search",
    color: "bg-green-500"
  },
  {
    id: 3,
    title: "投资决策",
    description: "确定投资金额",
    details: "根据风险偏好和资金情况，选择合适的投资金额",
    icon: "credit-card",
    color: "bg-yellow-500"
  },
  {
    id: 4,
    title: "收益分配",
    description: "获得投资回报",
    details: "项目收益按比例分配，透明的收益结算流程",
    icon: "chart-bar",
    color: "bg-purple-500"
  }
])

const partners = ref({
  platforms: [
    { id: 1, name: "抖音", logo: "https://via.placeholder.com/120x60/000000/FFFFFF?text=抖音" },
    { id: 2, name: "快手", logo: "https://via.placeholder.com/120x60/FF6B35/FFFFFF?text=快手" },
    { id: 3, name: "微博", logo: "https://via.placeholder.com/120x60/E6162D/FFFFFF?text=微博" }
  ],
  certification: [
    { id: 1, name: "金融许可", logo: "https://via.placeholder.com/120x60/FFD700/000000?text=金融许可" },
    { id: 2, name: "安全认证", logo: "https://via.placeholder.com/120x60/32CD32/FFFFFF?text=安全认证" }
  ],
  partners: [
    { id: 1, name: "投资机构A", logo: "https://via.placeholder.com/120x60/4169E1/FFFFFF?text=机构A" },
    { id: 2, name: "投资机构B", logo: "https://via.placeholder.com/120x60/DC143C/FFFFFF?text=机构B" },
    { id: 3, name: "投资机构C", logo: "https://via.placeholder.com/120x60/FF8C00/FFFFFF?text=机构C" }
  ]
})

const investmentRecords = ref([
  { investor: "张先生", time: "刚刚", amount: 100000 },
  { investor: "李女士", time: "2分钟前", amount: 200000 },
  { investor: "王先生", time: "5分钟前", amount: 150000 }
])

const newsCategories = ref(["全部", "行业动态", "投资资讯", "项目更新"])
const activeNewsCategory = ref("全部")

// 加载短剧数据
const loadDramaData = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await getPublicDramas({
      page: 1,
      pageSize: 12,
      status: 'funding' // 只获取募资中的短剧（剩余天数>0）
    })

    if (response.data && response.data.success) {
      dramas.value = response.data.data.list
    } else {
      error.value = '获取短剧数据失败'
    }
  } catch (err) {
    console.error('加载短剧数据失败:', err)
    error.value = '加载短剧数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 加载网站统计数据
const loadWebsiteStats = async () => {
  statsLoading.value = true
  statsError.value = null

  try {
    const response = await getWebsiteStats()

    if (response && response.success && response.data) {
      websiteStats.value = response.data
    } else {
      statsError.value = response?.message || '获取统计数据失败'
    }
  } catch (err) {
    console.error('加载统计数据失败:', err)
    statsError.value = '加载统计数据失败，请稍后重试'
  } finally {
    statsLoading.value = false
  }
}

// 启动实时投资人数变化
const startRealtimeInvestors = () => {
  investorTimer = setInterval(() => {
    // 在1575-1685之间随机变化，使用更自然的变化幅度
    const currentValue = realtimeInvestors.value
    const min = 1575
    const max = 1685

    // 生成相对当前值的小幅变化，使变化更自然
    const changeRange = 10 // 每次变化范围
    const minChange = Math.max(min, currentValue - changeRange)
    const maxChange = Math.min(max, currentValue + changeRange)

    realtimeInvestors.value = Math.floor(Math.random() * (maxChange - minChange + 1)) + minChange
  }, 4000) // 每4秒变化一次
}

// 启动用户轮播
const startUserRotation = () => {
  userTimer = setInterval(() => {
    if (websiteStats.value?.users && websiteStats.value.users.length > 0) {
      currentUserIndex.value = (currentUserIndex.value + 1) % websiteStats.value.users.length
    }
  }, 3000) // 每3秒切换一次用户
}







// 计算属性
const currentUser = computed(() => {
  if (websiteStats.value?.users && websiteStats.value.users.length > 0) {
    return websiteStats.value.users[currentUserIndex.value]
  }
  return null
})

// 计算进度百分比
const calculateProgress = (current: number, goal: number): number => {
  return Math.min(Math.round((current / goal) * 100), 100);
};

// 格式化货币
const formatCurrency = (value: number): string => {
  return (value / 10000).toFixed(0) + ' 万';
};

// 在首页只显示最多12个项目（2行，每行6个）
const displayedProjects = computed(() => {
  return dramas.value.slice(0, 12);
});

// 清理定时器
const clearTimers = () => {
  if (investorTimer) {
    clearInterval(investorTimer)
    investorTimer = null
  }
  if (userTimer) {
    clearInterval(userTimer)
    userTimer = null
  }
}



// 在组件挂载时加载数据
onMounted(() => {
  loadTags() // 加载标签数据
  loadDramaData()
  loadWebsiteStats()
  startRealtimeInvestors()
  startUserRotation()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  clearTimers()
})

// 导航到项目详情页
const navigateToProjectDetail = (projectId: number) => {
  router.push(`/project/${projectId}`);
};

// 处理投资点击
const handleInvestment = (project: Drama) => {
  // 跳转到项目详情页面的投资部分
  router.push(`/project/${project.id}#investment`);
};

// 新闻分类切换
const changeNewsCategory = (category: string) => {
  activeNewsCategory.value = category;
};


</script>

<template>
  <div class="home">
    <!-- 轮播图 -->
    <HomeBanner />

    <!-- 指标看板 -->
    <section class="py-8 bg-gradient-to-r from-blue-50 to-purple-50">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 已募资金总额 -->
          <div class="bg-white rounded-xl shadow-md p-6 flex items-center transform transition-all hover:scale-105 hover:shadow-lg">
            <div class="flex-shrink-0 bg-gradient-primary rounded-full p-4 mr-4">
              <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <div class="flex items-center">
                <h3 class="text-lg font-medium text-gray-500">已募资金总额</h3>
                <div class="ml-2 flex items-center text-green-500">
                  <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  <span class="text-xs ml-1">{{ websiteStats?.fundingGrowthRate || 36.4 }}%</span>
                </div>
              </div>
              <div class="text-3xl font-bold text-gray-800 mt-1">
                {{ websiteStats?.totalRaisedAmount?.toFixed(2) || '0.00' }} 万元
              </div>
              <div class="text-sm text-gray-500 mt-1">募资金额增长 <span class="text-primary font-medium">{{ websiteStats?.fundingGrowthRate || 36.4 }}%</span></div>
            </div>
          </div>
          
          <!-- 今日投资总额 -->
          <div class="bg-white rounded-xl shadow-md p-6 flex items-center transform transition-all hover:scale-105 hover:shadow-lg">
            <div class="flex-shrink-0 bg-gradient-primary rounded-full p-4 mr-4">
              <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-500">今日投资总额</h3>
              <div class="text-2xl font-bold text-gray-800 mt-1">
                {{ websiteStats?.todayInvestmentAmount?.toFixed(2) || '0.00' }} 万 /
                {{ websiteStats?.historicalInvestmentTotal?.toFixed(0) || '0' }} 万
              </div>
              <div class="text-sm text-gray-500 mt-1">
                占比 <span class="text-primary font-medium">{{ websiteStats?.todayInvestmentRatio || 0 }}</span>
              </div>
            </div>
          </div>
          
          <!-- 实时投资人 -->
          <div class="bg-white rounded-xl shadow-md p-6 flex items-center transform transition-all hover:scale-105 hover:shadow-lg">
            <div class="flex-shrink-0 bg-gradient-primary rounded-full p-4 mr-4">
              <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-500">实时投资人</h3>
              <div class="text-3xl font-bold text-gray-800 mt-1">
                {{ realtimeInvestors }} <span class="text-sm font-normal">位</span>
              </div>
              <div class="text-sm text-gray-500 mt-1">
                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded">实时在线</span>
                <span v-if="currentUser" class="ml-2 text-xs">
                  {{ currentUser.username }} <span class="text-primary">{{ currentUser.userType }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 项目亮点 -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">项目亮点</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            剧募募汇集行业精英，打造优质短剧IP，为投资人提供高质量、高回报的投资机会
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div 
            v-for="item in highlights" 
            :key="item.id" 
            class="card hover:shadow-lg transition-shadow duration-300 overflow-hidden"
          >
            <div :class="['w-16 h-16 rounded-full flex items-center justify-center text-white mb-6', item.color]">
              <svg v-if="item.icon === 'chart-pie'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
              </svg>
              <svg v-else-if="item.icon === 'users'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
              <svg v-else-if="item.icon === 'shield-check'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <svg v-else-if="item.icon === 'trending-up'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            
            <h3 class="text-xl font-semibold mb-2">{{ item.title }}</h3>
            <p class="text-gray-600 mb-4">{{ item.description }}</p>
            
            <div class="mt-auto pt-4 border-t border-gray-100">
              <p class="text-2xl font-bold text-primary">{{ item.roi }}%</p>
            </div>
          </div>
        </div>
        
        <div class="text-center mt-12">
          <RouterLink to="/investment" class="btn btn-primary px-8 py-3 text-lg">
            查看投资方案
          </RouterLink>
        </div>
      </div>
    </section>
    
    <!-- 募资中短剧展示区 -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">募资中短剧</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            精选优质短剧IP项目，打造爆款内容，共享行业红利
          </p>
        </div>
        
        <!-- 加载中状态 -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="w-12 h-12 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
        </div>
        
        <!-- 错误提示 -->
        <div v-else-if="error" class="text-center py-12">
          <svg class="w-16 h-16 mx-auto text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <p class="mt-4 text-lg text-red-600">{{ error }}</p>
          <button 
            @click="loadDramaData" 
            class="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
          >
            重试
          </button>
        </div>
        
        <!-- 无数据提示 -->
        <div v-else-if="dramas.length === 0" class="text-center py-12">
          <svg class="w-16 h-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
          <p class="mt-4 text-lg text-gray-600">暂无募资中的短剧项目</p>
        </div>
        
        <!-- 响应式网格布局 - 限制只显示12个项目（2行x6列） -->
        <div v-else class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-x-4 gap-y-8">
          <!-- 项目卡片 -->
          <div
            v-for="project in displayedProjects"
            :key="project.id"
            class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 relative group border border-gray-100 hover:border-gray-200 cursor-pointer flex flex-col h-full"
            @click="navigateToProjectDetail(project.id)"
          >
            <!-- 封面图区域 (3:4比例) -->
            <div class="relative pt-[133.33%] bg-gray-100 overflow-hidden">
              <!-- 封面图 -->
              <div class="absolute inset-0 flex items-center justify-center transition-transform duration-500 group-hover:scale-105">
                <img
                  v-if="project.cover"
                  :src="project.cover"
                  :alt="project.title"
                  class="w-full h-full object-cover"
                />
                <div v-else class="w-full h-full flex items-center justify-center bg-purple-100">
                  <svg class="w-20 h-20 md:w-24 md:h-24 lg:w-20 lg:h-20 text-primary" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,17.27L18.18,21l-1.64-7.03L22,9.24l-7.19-0.61L12,2L9.19,8.63L2,9.24l5.46,4.73L5.82,21L12,17.27z" />
                  </svg>
                </div>
              </div>
              
              <!-- 剩余天数角标 -->
              <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 text-xs md:text-sm lg:text-xs font-bold rounded-md shadow-md">
                剩余 {{ project.remainingDays }} 天
              </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="p-3 md:p-4 lg:p-3 flex-1 flex flex-col">
              <!-- 标题 -->
              <h3 class="text-sm md:text-base lg:text-sm font-bold mb-2 h-6 md:h-7 lg:h-6 hover:text-primary transition-colors overflow-hidden">
                <div class="whitespace-nowrap animate-scroll-if-overflow">
                  {{ project.title }}
                </div>
              </h3>
              
              <!-- 类型标签 -->
              <div class="flex gap-1 mb-2 overflow-hidden">
                <DramaTag
                  v-for="tag in parseTagData(project.tags)"
                  :key="tag.id || tag.name"
                  :tag="tag"
                  class="text-[10px] md:text-xs lg:text-[10px] flex-shrink-0"
                />
              </div>
              
              <!-- 募资进度条 -->
              <div>
                <div class="flex justify-between text-xs md:text-sm lg:text-xs mb-1">
                  <span class="text-gray-600">已筹 {{ formatCurrency(project.currentFunding) }}</span>
                  <span class="font-medium">{{ calculateProgress(project.currentFunding, project.fundingGoal) }}%</span>
                </div>
                <div class="h-2 md:h-2.5 lg:h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    class="h-full rounded-full bg-primary" 
                    :style="{
                      width: `${calculateProgress(project.currentFunding, project.fundingGoal)}%`
                    }"
                  ></div>
                </div>
                <div class="text-[10px] md:text-xs lg:text-[10px] text-gray-500 mt-1">
                  目标 {{ formatCurrency(project.fundingGoal) }}
                </div>
              </div>
            </div>
            
            <!-- 常驻按钮组 -->
            <div class="bg-white pt-2 px-3 pb-3 flex gap-2 mt-auto">
              <RouterLink
                :to="`/project/${project.id}`"
                class="flex-1 py-2 px-3 rounded-lg flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-800 text-center font-medium text-xs transition-colors"
                @click.stop
              >
                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                查看详情
              </RouterLink>
              <button
                @click="handleInvestment(project)"
                class="flex-1 py-2 px-3 rounded-lg flex items-center justify-center bg-primary hover:bg-primary-dark text-white text-center font-medium text-xs transition-colors"
              >
                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z" />
                </svg>
                参与众筹
              </button>
            </div>
          </div>
        </div>
        
        <!-- 查看更多按钮 - 链接到短剧筹募页面 -->
        <div class="text-center mt-12">
          <RouterLink to="/projects" class="btn btn-primary px-8 py-3 text-lg">
            查看更多短剧
          </RouterLink>
        </div>
      </div>
    </section>
    
    <!-- 投资流程时间轴 -->
    <section class="py-16 bg-gradient-to-r from-blue-50 to-purple-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">投资流程</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            从选择项目到收益分配，全流程公开透明
          </p>
        </div>
        
        <!-- 桌面端横向时间轴 -->
        <div class="hidden md:block">
          <div class="relative">
            <!-- 连接线 -->
            <div class="absolute top-1/4 left-0 right-0 h-0.5 bg-gray-200"></div>
            
            <!-- 流程步骤 -->
            <div class="grid grid-cols-4 gap-8 relative">
              <div 
                v-for="step in investmentProcess" 
                :key="step.id" 
                class="flex flex-col items-center text-center relative"
              >
                <!-- 序号圆圈 -->
                <div 
                  class="w-16 h-16 rounded-full bg-white border-4 border-primary flex items-center justify-center text-xl font-bold z-10 mb-6 relative"
                  @mouseenter="showTooltip(step.id)"
                  @mouseleave="hideTooltip()"
                >
                  {{ step.id }}
                  
                  <!-- 工具提示 -->
                  <div 
                    v-show="activeTooltip === step.id"
                    class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 bg-white p-4 rounded-lg shadow-lg text-left z-20 text-sm"
                  >
                    <p class="text-gray-700">{{ step.details }}</p>
                    <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white rotate-45"></div>
                  </div>
                </div>
                
                <!-- 图标 -->
                <div class="bg-gradient-primary w-12 h-12 rounded-full flex items-center justify-center text-white mb-4">
                  <svg v-if="step.icon === 'search'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <svg v-else-if="step.icon === 'document-text'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <svg v-else-if="step.icon === 'film'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
                  </svg>
                  <svg v-else-if="step.icon === 'cash'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                
                <!-- 标题和描述 -->
                <h3 class="text-lg font-bold mb-2">{{ step.title }}</h3>
                <p class="text-gray-600 text-sm">{{ step.description }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 移动端纵向时间轴 -->
        <div class="md:hidden">
          <div class="relative pl-10">
            <!-- 垂直连接线 -->
            <div class="absolute top-0 bottom-0 left-4 w-0.5 bg-gray-200"></div>
            
            <!-- 流程步骤 -->
            <div class="space-y-8">
              <div 
                v-for="step in investmentProcess" 
                :key="step.id" 
                class="relative"
              >
                <!-- 序号圆圈 -->
                <div 
                  class="absolute left-0 top-0 transform -translate-x-1/2 w-10 h-10 rounded-full bg-white border-2 border-primary flex items-center justify-center text-lg font-bold z-10"
                  @click="activeTooltip === step.id ? hideTooltip() : showTooltip(step.id)"
                >
                  {{ step.id }}
                </div>
                
                <!-- 内容卡片 -->
                <div class="bg-white rounded-lg shadow-sm p-4">
                  <div class="flex items-center mb-3">
                    <!-- 图标 -->
                    <div class="bg-gradient-primary w-10 h-10 rounded-full flex items-center justify-center text-white mr-3 flex-shrink-0">
                      <svg v-if="step.icon === 'search'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      <svg v-else-if="step.icon === 'document-text'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <svg v-else-if="step.icon === 'film'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
                      </svg>
                      <svg v-else-if="step.icon === 'cash'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    
                    <!-- 标题 -->
                    <h3 class="text-lg font-bold">{{ step.title }}</h3>
                  </div>
                  
                  <!-- 描述 -->
                  <p class="text-gray-600 text-sm">{{ step.description }}</p>
                  
                  <!-- 详情（点击后展开） -->
                  <div v-if="activeTooltip === step.id" class="mt-3 text-sm text-gray-700 bg-gray-50 p-3 rounded">
                    {{ step.details }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 风险提示区域 -->
        <div class="mt-16 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div class="flex items-start">
            <div class="flex-shrink-0 mr-3">
              <svg class="w-6 h-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div>
              <h4 class="text-lg font-semibold text-yellow-800 mb-2">风险提示</h4>
              <ul class="text-sm text-yellow-700 space-y-2">
                <li>• 最低投资额：人民币5万元起</li>
                <li>• 资金托管银行：招商银行（上海分行）</li>
                <li>• 标准投资期限：18个月，期满可选择退出或继续持有</li>
                <li>• 退出机制：投资满6个月后可申请提前退出，将收取本金5%的提前退出费</li>
                <li>• 投资有风险，短视频行业受政策监管影响较大，请投资者理性决策</li>
              </ul>
            </div>
          </div>
        </div>
        
        <!-- 了解更多按钮 -->
        <div class="text-center mt-8">
          <RouterLink to="/investment" class="btn bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-lg transition-all">
            了解详细投资方案
          </RouterLink>
        </div>
      </div>
    </section>
    
    <!-- 团队权威展示 -->
    <section class="py-16 bg-white relative overflow-hidden">
      <!-- 背景关键词云 -->
      <div class="absolute inset-0 flex flex-wrap justify-center items-center opacity-5 select-none">
        <span v-for="(keyword, index) in ['IP孵化', '内容策划', '流量运营', '商业变现', '编剧', '制片', '渠道分发', '风险控制', '数据分析', '用户画像', '创意指导', '投融资', 'AI算法', '资源整合', '内容评估', '成本控制']" 
              :key="index"
              class="mx-4 my-2 text-primary"
              :class="{'text-4xl': index % 5 === 0, 'text-3xl': index % 5 === 1, 'text-5xl': index % 5 === 2, 'text-2xl': index % 5 === 3, 'text-6xl': index % 5 === 4}"
        >
          {{ keyword }}
        </span>
      </div>
      
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">团队权威</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            顶尖行业专家助力，打造优质短剧内容价值
          </p>
        </div>
        
        <!-- 核心团队 -->
        <div class="mb-16">
          <h3 class="text-2xl font-bold mb-8 text-center">核心团队</h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div 
              v-for="member in teamMembers" 
              :key="`core-${member.id}`"
              class="member-card relative bg-white rounded-lg shadow-md transition-all duration-300 hover:shadow-xl"
              @mouseenter="setHoveredMember(member)"
              @mouseleave="clearHoveredMember()"
            >
              <!-- 基本信息 -->
              <div class="p-6 text-center">
                <div class="w-24 h-24 mx-auto mb-4 bg-gradient-primary rounded-full flex items-center justify-center overflow-hidden">
                  <img
                    :src="member.avatar"
                    :alt="member.name"
                    class="w-full h-full object-cover"
                    @error="handleImageError"
                  />
                </div>
                <h4 class="text-xl font-bold mb-1">{{ member.name }}</h4>
                <p class="text-gray-600">{{ member.position }}</p>
              </div>
              
              <!-- 悬停展开面板 -->
              <div 
                class="absolute inset-0 bg-white rounded-lg shadow-xl p-6 transition-all duration-300 z-20"
                :class="hoveredMember === member ? 'opacity-100 transform scale-105' : 'opacity-0 pointer-events-none'"
              >
                <div class="flex items-center mb-4">
                  <div class="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center flex-shrink-0 overflow-hidden">
                    <img
                      :src="member.avatar"
                      :alt="member.name"
                      class="w-full h-full object-cover"
                      @error="handleImageError"
                    />
                  </div>
                  <div class="ml-4">
                    <h4 class="text-xl font-bold">{{ member.name }}</h4>
                    <p class="text-primary font-medium">{{ member.position }}</p>
                  </div>
                </div>
                
                <p class="text-gray-600 text-sm mb-4">{{ member.experience }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="text-center mt-10">
          <RouterLink to="/team" class="btn bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-lg transition-all">
            查看完整团队
          </RouterLink>
        </div>
      </div>
    </section>
    
    <!-- 合作伙伴Logo墙 -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">合作伙伴</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            剧募募携手行业顶尖机构，共创短剧内容生态
          </p>
        </div>
        
        <!-- 合作媒体 -->
        <div class="mb-12">
          <h3 class="text-2xl font-bold mb-6 text-center">合作媒体</h3>
          <div class="logo-scroller overflow-hidden relative">
            <div class="scroller-inner flex animate-scroll">
              <div 
                v-for="partner in partners.platforms" 
                :key="`media-${partner.id}-${Math.random()}`"
                class="logo-item flex-shrink-0 mx-6 transition-transform duration-300 hover:scale-120 cursor-pointer"

              >
                <div class="w-32 h-16 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-all p-2">
                  <span class="text-lg font-bold text-gray-700">{{ partner.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 行业认证 -->
        <div class="mb-12">
          <h3 class="text-2xl font-bold mb-6 text-center">行业认证</h3>
          <div class="logo-scroller overflow-hidden relative">
            <div class="scroller-inner flex animate-scroll-reverse">
              <div 
                v-for="partner in partners.certification" 
                :key="`cert-${partner.id}-${Math.random()}`"
                class="logo-item flex-shrink-0 mx-6 transition-transform duration-300 hover:scale-120 cursor-pointer"
              >
                <div class="w-32 h-16 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-all p-2">
                  <span class="text-lg font-bold text-gray-700">{{ partner.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 战略伙伴 -->
        <div class="mb-12">
          <h3 class="text-2xl font-bold mb-6 text-center">战略伙伴</h3>
          <div class="logo-scroller overflow-hidden relative">
            <div class="scroller-inner flex animate-scroll">
              <div 
                v-for="partner in partners.partners" 
                :key="`partner-${partner.id}-${Math.random()}`"
                class="logo-item flex-shrink-0 mx-6 transition-transform duration-300 hover:scale-120 cursor-pointer"
              >
                <div class="w-32 h-16 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-all p-2">
                  <span class="text-lg font-bold text-gray-700">{{ partner.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </section>
    
    <!-- 信任锚点固定横幅 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 z-40">
      <!-- 实时弹幕区域 -->
      <div class="bg-gradient-to-r from-blue-50 to-purple-50 py-2 overflow-hidden relative">
        <div class="danmu-container">
          <div class="danmu-scroller">
            <div 
              v-for="(record, index) in investmentRecords" 
              :key="index" 
              class="inline-block mx-8 text-sm text-gray-700"
            >
              <span class="font-semibold text-primary">{{ record.investor }}</span>
              <span> {{ record.time }}投资 </span>
              <span class="font-bold text-primary">¥{{ record.amount.toLocaleString() }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 信任标语栏 -->
      <div class="container mx-auto px-4 py-3">
        <!-- 桌面版布局 -->
        <div class="hidden md:flex justify-between items-center">
          <!-- 左侧安全锁及托管信息 -->
          <div class="flex items-center">
            <div class="mr-4 flex items-center">
              <svg class="w-5 h-5 text-green-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              <span class="text-sm font-medium">SSL 安全加密</span>
            </div>
            
            <div class="mr-4 flex items-center">
              <svg class="w-5 h-5 text-green-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <span class="text-sm font-medium">资金由招商银行第三方托管</span>
            </div>
            
            <div class="flex items-center">
              <svg class="w-5 h-5 text-green-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
              <span class="text-sm font-medium">投资收益定期结算</span>
            </div>
          </div>
          

        </div>
        
        <!-- 移动版布局 -->
        <div class="flex md:hidden justify-between items-center">
          <!-- 左侧滚动安全信息 -->
          <div class="overflow-hidden relative w-4/5">
            <div class="trust-info-scroller whitespace-nowrap">
              <div class="inline-flex items-center mx-3">
                <svg class="w-4 h-4 text-green-500 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <span class="text-xs font-medium">SSL 安全加密</span>
              </div>
              <div class="inline-flex items-center mx-3">
                <svg class="w-4 h-4 text-green-500 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <span class="text-xs font-medium">资金由招商银行第三方托管</span>
              </div>
              <div class="inline-flex items-center mx-3">
                <svg class="w-4 h-4 text-green-500 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
                <span class="text-xs font-medium">投资收益定期结算</span>
              </div>
            </div>
          </div>
          

        </div>
      </div>
    </div>
    
    <!-- 为底部固定条留出空间 -->
    <div class="h-[112px]"></div>
    
    <!-- 行业快讯 News Ticker -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">行业快讯</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            实时掌握短剧行业动态，了解最新政策与市场趋势
          </p>
        </div>
        
        <div class="flex flex-col lg:flex-row gap-8">
          <!-- 左侧垂直标签导航 -->
          <div class="w-full lg:w-64 flex-shrink-0">
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
              <div class="divide-y divide-gray-100">
                <button
                  v-for="(category, index) in newsCategories"
                  :key="index"
                  @click="changeNewsCategory(category)"
                  class="w-full py-4 px-6 text-left font-medium transition-colors relative flex items-center"
                  :class="activeNewsCategory === category ? 'text-primary bg-primary/5' : 'text-gray-700 hover:bg-gray-50'"
                >
                  <!-- 激活指示图标 -->
                  <svg v-if="activeNewsCategory === category" class="w-5 h-5 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                  <span>{{ category }}</span>
                  
                  <!-- 激活指示条 -->
                  <div 
                    v-if="activeNewsCategory === category" 
                    class="absolute left-0 top-0 bottom-0 w-1 bg-primary"
                  ></div>
                </button>
              </div>
            </div>
            
            <!-- 查看全部按钮 -->
            <div class="mt-4">
              <RouterLink to="/news" class="w-full py-3 px-6 bg-white rounded-xl shadow-sm border border-gray-200 flex justify-center items-center font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                <span>查看全部新闻</span>
                <svg class="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </RouterLink>
            </div>
          </div>
          
          <!-- 右侧新闻卡片网格 -->
          <div class="flex-grow">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 新闻卡片 -->
              <div 
                v-for="(_, index) in 6"
                :key="index"
                class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 flex flex-col"
              >
                <!-- 封面图 (16:9比例) -->
                <div class="relative pt-[56.25%] bg-gray-200 overflow-hidden">
                  <!-- 封面图 SVG 占位 -->
                  <div class="absolute inset-0 flex items-center justify-center">
                    <svg class="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v12a2 2 0 01-2 2z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16 2v4M8 2v4M3 10h18" />
                    </svg>
                  </div>
                  
                  <!-- 媒体Logo -->
                  <div class="absolute top-2 left-2 w-6 h-6 rounded-full bg-white shadow-md flex items-center justify-center overflow-hidden">
                    <div class="w-4 h-4 bg-primary rounded-full"></div>
                  </div>
                  
                  <!-- New标签 (随机显示) -->
                  <div v-if="index % 3 === 0" class="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                    New
                  </div>
                </div>
                
                <!-- 内容区域 -->
                <div class="p-4 flex-grow flex flex-col">
                  <!-- 标题 -->
                  <h3 class="font-bold text-gray-800 mb-2 line-clamp-2 hover:text-primary hover:underline transition-colors">
                    <a href="#" class="hover:text-primary">短剧市场{{ index + 1 }}月报告：{{ activeNewsCategory }}趋势分析</a>
                  </h3>
                  
                  <!-- 摘要 -->
                  <p class="text-sm text-gray-600 mb-4 line-clamp-2 flex-grow">
                    {{ activeNewsCategory }}最新动态分析，短剧市场增长态势明显，投资热度持续上升，预计下半年将迎来新一轮爆发期。
                  </p>
                  
                  <!-- 底部信息 -->
                  <div class="flex justify-between items-center mt-auto pt-3 border-t border-gray-100">
                    <div class="flex items-center">
                      <span class="text-xs text-gray-500">{{ new Date(Date.now() - index * 86400000).toLocaleDateString() }}</span>
                      <span class="mx-2 text-gray-300">|</span>
                      <span class="text-xs text-gray-500 flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        {{ (1000 + index * 123) }}
                      </span>
                    </div>
                    
                    <!-- 分享按钮 (悬浮显示) -->
                    <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                      <div class="flex space-x-2">
                        <button class="w-6 h-6 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors">
                          <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M22.05 7.54a4.47 4.47 0 0 0-3.3-1.46 4.53 4.53 0 0 0-4.53 4.53c0 .35.04.7.08 1.05A12.9 12.9 0 0 1 5 6.89a4.3 4.3 0 0 0-.65 2.26c0 1.53.8 2.87 2 3.64a4.3 4.3 0 0 1-2.02-.57v.08a4.55 4.55 0 0 0 3.63 4.44c-.4.08-.8.13-1.21.13-.3 0-.56-.03-.87-.09a4.54 4.54 0 0 0 4.22 3.15 9.56 9.56 0 0 1-5.66 1.94c-.34 0-.7-.03-1.05-.08a13.36 13.36 0 0 0 7.04 2.04c8.08 0 12.52-6.7 12.52-12.52 0-.19-.01-.37-.01-.56a8.93 8.93 0 0 0 2.2-2.27c-.82.38-1.69.62-2.6.72a4.37 4.37 0 0 0 1.97-2.51z"></path>
                          </svg>
                        </button>
                        <button class="w-6 h-6 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors">
                          <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21.385 15.992a1.599 1.599 0 0 1-1.695.054 8.217 8.217 0 0 0-4.948-1.158 1.574 1.574 0 0 1-1.673-1.472A1.574 1.574 0 0 1 14.54 11.72a11.321 11.321 0 0 1 6.814 1.593 1.575 1.575 0 0 1 .031 2.68zm.358-4.997a.999.999 0 0 1-1.039.037 12.133 12.133 0 0 0-7.128-1.593 1 1 0 0 1-1.066-.932.998.998 0 0 1 .933-1.066 14.133 14.133 0 0 1 8.298 1.855 1 1 0 0 1 .002 1.699zm-9.53-7.562a15 15 0 0 0-9.5 1.727A1.001 1.001 0 0 0 2.34 6.67c.36 0 .72-.13.996-.379A13 13 0 0 1 12 4.557a13 13 0 0 1 8.663 1.734.994.994 0 0 0 1.38-.28 1 1 0 0 0-.279-1.38 15 15 0 0 0-9.5-1.727zm-3.846 9.707a2.999 2.999 0 1 0 5.998 0 2.999 2.999 0 0 0-5.998 0z"></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

  </div>
</template>

<style scoped>
.btn-text-gradient {
  background: linear-gradient(to right, var(--color-purple-gradient-start), var(--color-purple-gradient-end));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent; /* 使用标准color属性替代非标准的text-fill-color */
}

/* 环形进度条动画 */
@keyframes progress {
  0% {
    stroke-dasharray: 0 100;
  }
}

svg path:nth-child(2) {
  animation: progress 1.5s ease-out forwards;
}

/* Logo墙滚动动画 */
.hover\:scale-120:hover {
  transform: scale(1.2);
}

.logo-scroller {
  mask-image: linear-gradient(to right, transparent, black 5%, black 95%, transparent);
}

.scroller-inner {
  width: max-content;
  padding-block: 1rem;
}

@keyframes scroll {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-50% - 0.75rem));
  }
}

@keyframes scroll-reverse {
  from {
    transform: translateX(calc(-50% - 0.75rem));
  }
  to {
    transform: translateX(0);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

.animate-scroll-reverse {
  animation: scroll-reverse 30s linear infinite;
}

/* 暂停滚动 */
.logo-scroller:hover .scroller-inner {
  animation-play-state: paused;
}

/* 团队成员卡片 */
.member-card {
  height: 260px;
  overflow: hidden;
}

.member-card:hover {
  z-index: 30;
}

/* 弹幕动画 */
.danmu-container {
  width: 100%;
  overflow: hidden;
}

.danmu-scroller {
  display: inline-block;
  white-space: nowrap;
  animation: danmu-scroll 30s linear infinite;
  padding: 2px 0;
}

@keyframes danmu-scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 移动端信任信息滚动 */
.trust-info-scroller {
  display: inline-block;
  white-space: nowrap;
  animation: trust-info-scroll 15s linear infinite;
  padding: 2px 0;
}

@keyframes trust-info-scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-120%);
  }
}

/* 标题滚动动画 */
.animate-scroll-if-overflow {
  display: inline-block;
  animation: scroll-text 8s linear infinite;
  animation-play-state: paused;
}

/* 当文本溢出时启用滚动动画 */
h3:hover .animate-scroll-if-overflow {
  animation-play-state: running;
}

@keyframes scroll-text {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(0);
  }
  75% {
    transform: translateX(calc(-100% + 100px));
  }
  100% {
    transform: translateX(calc(-100% + 100px));
  }
}

/* 为底部栏留出空间 */
#app {
  padding-bottom: 112px;
}
</style> 