<script setup>
import { RouterLink, RouterView } from 'vue-router'
import Header from './components/Header.vue'
import Footer from './components/Footer.vue'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// 判断当前是否是管理后台页面
const isAdminPage = computed(() => {
  return route.path.startsWith('/admin')
})

// 判断当前是否是登录或注册页面
const isAuthPage = computed(() => {
  return route.path === '/login' || route.path === '/register'
})

// 判断是否需要显示头部和底部
const showHeaderFooter = computed(() => {
  return !isAdminPage.value && !isAuthPage.value
})
</script>

<template>
  <!-- 只在非管理后台页面且非登录注册页面显示Header -->
  <Header v-if="showHeaderFooter" />
  
  <main :class="{
    'w-full pt-20': showHeaderFooter,
    'w-full h-full': isAdminPage || isAuthPage
  }">
    <RouterView />
  </main>
  
  <!-- 只在非管理后台页面且非登录注册页面显示Footer -->
  <Footer v-if="showHeaderFooter" />
</template>

<style>
/* 全局样式将在main.css中定义 */
body, html {
  margin: 0;
  padding: 0;
  height: 100%;
}
</style> 