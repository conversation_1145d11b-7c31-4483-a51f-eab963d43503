<script setup>
import { ref, onMounted } from 'vue'
import { RouterLink } from 'vue-router'
import { getPublicFunds } from '../api/public/fundApi'

// 数据状态
const loading = ref(false)
const error = ref(null)
const funds = ref([])
const showDebugInfo = ref(false)

// 加载基金数据
const loadFunds = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await getPublicFunds({
      page: 1,
      limit: 10
    })

    console.log('API响应:', response)

    if (response.data && response.data.success) {
      funds.value = response.data.data?.list || []
      console.log('基金数据加载成功:', funds.value)
    } else {
      // 如果没有数据，显示空状态而不是错误
      funds.value = []
      console.log('暂无基金数据')
    }
  } catch (err) {
    console.error('加载基金数据失败:', err)
    // 网络错误或API错误才显示错误信息
    if (err.response?.status >= 500) {
      error.value = '服务器错误，请稍后重试'
    } else if (err.response?.status === 404) {
      funds.value = []
    } else {
      error.value = '加载基金数据失败，请刷新页面重试'
    }
  } finally {
    loading.value = false
  }
}

// 格式化金额显示
const formatAmount = (amount) => {
  if (!amount) return '未公布'
  if (amount >= 100000000) {
    return `${(amount / 100000000).toFixed(1)}亿`
  } else if (amount >= 10000) {
    return `${(amount / 10000).toFixed(0)}万`
  } else {
    return `${amount}`
  }
}

// 获取基金类型显示名称
const getFundTypeDisplay = (type) => {
  const typeMap = {
    'equity': '股权型',
    'debt': '债权型',
    'mixed': '混合型'
  }
  return typeMap[type] || type
}

// 获取风险等级颜色
const getRiskColor = (risk) => {
  const colorMap = {
    'R1': 'bg-green-100 text-green-800',
    'R2': 'bg-blue-100 text-blue-800',
    'R3': 'bg-yellow-100 text-yellow-800',
    'R4': 'bg-orange-100 text-orange-800',
    'R5': 'bg-red-100 text-red-800'
  }
  return colorMap[risk] || 'bg-gray-100 text-gray-800'
}

// 页面加载时获取数据
onMounted(() => {
  loadFunds()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面标题 -->
    <section class="bg-gray-50 py-4">
      <div class="container mx-auto px-4">
        <div class="bg-gradient-primary text-white py-12 rounded-lg text-center">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">旅文基金</h1>
          <div class="max-w-4xl mx-auto">
            <p class="text-lg mb-4 opacity-90">
              剧投投旅文基金专注于文化旅游产业投资，整合优质资源，推动文旅产业创新发展。通过专业的投资管理团队和科学的风险控制体系，为投资者提供专业的基金管理服务。
            </p>
            <p class="text-lg mb-8 opacity-90">
              我们关注具有文化底蕴和创新潜力的旅游项目，包括特色小镇、主题公园、文化IP开发、数字文旅等多个领域，致力于打造中国文旅产业投资的标杆。
            </p>

            <!-- 数据展示 -->
            <div class="flex justify-center items-center space-x-12 text-white">
              <div class="text-center">
                <div class="text-2xl font-bold">5亿+</div>
                <div class="text-sm opacity-80">管理资金规模</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold">20+</div>
                <div class="text-sm opacity-80">投资项目数量</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold">专业</div>
                <div class="text-sm opacity-80">投资管理团队</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <div class="container mx-auto px-4">
      <!-- 免责声明 -->
      <section class="py-6">
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-lg">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <p class="text-yellow-700 text-sm">
              本平台所有基金信息仅供展示参考，不构成投资建议。投资有风险，入市需谨慎。请投资者根据自身风险承受能力谨慎决策。
            </p>
          </div>
        </div>
      </section>

      <!-- 基金产品展示 -->
      <section class="py-6">
        <!-- 调试信息 -->
        <div v-if="showDebugInfo" class="mb-4 p-3 bg-gray-100 rounded text-xs">
          <div>API状态: {{ loading ? '加载中...' : '已完成' }}</div>
          <div>基金数量: {{ funds.length }}</div>
          <div>错误信息: {{ error || '无' }}</div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="flex items-center justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>

        <!-- 错误提示 -->
        <div v-else-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
          {{ error }}
          <button @click="loadFunds" class="ml-4 text-red-800 underline">重试</button>
        </div>

        <!-- 基金列表 -->
        <div v-else-if="funds.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="fund in funds"
            :key="fund.id"
            class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-300"
          >
            <div class="flex justify-between items-start mb-4">
              <h3 class="font-bold text-lg text-gray-900 leading-tight">{{ fund.title }}</h3>
              <span :class="getRiskColor(fund.risk)" class="px-2 py-1 rounded-md text-sm font-medium">
                {{ fund.risk }}
              </span>
            </div>

            <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ fund.description || '暂无描述' }}</p>

            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-500">基金代码：</span>
                <span class="font-medium">{{ fund.code || fund.id }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">基金类型：</span>
                <span class="font-medium">{{ getFundTypeDisplay(fund.type) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">起投金额：</span>
                <span class="font-medium">{{ formatAmount(fund.minInvestment) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">目标规模：</span>
                <span class="font-medium">{{ formatAmount(fund.targetSize) }}</span>
              </div>
            </div>

            <div class="mt-4 pt-4 border-t border-gray-100">
              <RouterLink
                :to="`/funds/${fund.code || fund.id}`"
                class="block w-full py-2 bg-primary hover:bg-primary-dark text-white text-center rounded-lg transition-colors text-sm font-medium"
              >
                查看详情
              </RouterLink>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="text-center py-8">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p class="text-gray-500">暂无基金产品</p>
        </div>
      </section>

      <!-- 服务介绍 -->
      <section class="py-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-white border border-gray-200 rounded-lg">
            <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white mx-auto mb-3">
              <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 class="font-semibold mb-1">基金管理</h3>
            <p class="text-gray-600 text-sm">专业的基金管理团队，严格的风险控制体系</p>
          </div>

          <div class="text-center p-4 bg-white border border-gray-200 rounded-lg">
            <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white mx-auto mb-3">
              <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <h3 class="font-semibold mb-1">信息披露</h3>
            <p class="text-gray-600 text-sm">定期发布基金运营报告，保持信息透明</p>
          </div>

          <div class="text-center p-4 bg-white border border-gray-200 rounded-lg">
            <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white mx-auto mb-3">
              <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 class="font-semibold mb-1">客户服务</h3>
            <p class="text-gray-600 text-sm">专业的客户服务团队，提供全方位支持</p>
          </div>
        </div>
      </section>

      <!-- 联系我们 -->
      <section class="py-6">
        <div class="bg-white rounded-lg p-6 text-center">
          <h2 class="text-xl font-bold mb-3">联系我们</h2>
          <p class="text-gray-600 mb-4 text-sm">如需了解更多基金信息，请联系我们的专业团队</p>
          <div class="flex flex-col sm:flex-row gap-3 justify-center">
            <a href="tel:************" class="inline-flex items-center justify-center px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors text-sm">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              ************
            </a>
            <a href="mailto:<EMAIL>" class="inline-flex items-center justify-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors text-sm">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <EMAIL>
            </a>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>