<template>
  <section class="bg-gray-50 py-4">
    <div class="container mx-auto px-4">
      <div class="relative overflow-hidden bg-gray-900 h-[550px] md:h-[650px] rounded-lg">
        <!-- 加载状态 -->
        <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-gray-800 rounded-lg">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
        </div>

        <!-- 错误提示 -->
        <div v-else-if="error" class="absolute inset-0 flex items-center justify-center bg-gray-800 text-white rounded-lg">
          <div class="text-center">
            <p class="mb-4">{{ error }}</p>
            <button @click="loadBanners" class="px-4 py-3 bg-primary rounded-md hover:bg-primary-dark">重试</button>
          </div>
        </div>

        <!-- 轮播内容 -->
        <div v-else-if="banners.length > 0">
          <!-- 轮播项 -->
          <div
            v-for="(item, index) in banners"
            :key="item.id"
            class="absolute inset-0 transition-opacity duration-500 cursor-pointer rounded-lg overflow-hidden"
            :class="index === currentSlide ? 'opacity-100 z-10' : 'opacity-0 z-0'"
            @click="handleBannerClick(item)"
          >
            <!-- 背景图片 (优先显示) -->
            <div v-if="item.imageUrl" class="absolute inset-0 z-0">
              <img
                :src="item.imageUrl"
                :alt="item.title"
                class="h-full w-full object-cover"
              >
            </div>

            <!-- 背景颜色 (仅在没有图片时显示) -->
            <div
              v-else-if="item.backgroundColor"
              class="absolute inset-0 z-0"
              :style="{ backgroundColor: item.backgroundColor }"
            ></div>

            <!-- 默认背景 (当图片和背景色都没有时) -->
            <div
              v-else
              class="absolute inset-0 z-0 bg-primary"
            ></div>
        
            <!-- 内容叠加 -->
            <div class="absolute bottom-0 left-0 right-0 z-20 p-8 md:p-16" :style="{ color: item.textColor || 'white' }">
              <h2 class="text-3xl md:text-5xl font-bold mb-3">{{ item.title }}</h2>
              <p v-if="item.subtitle" class="text-xl md:text-2xl mb-6 max-w-2xl">{{ item.subtitle }}</p>
              <RouterLink
                v-if="item.linkUrl"
                :to="item.linkUrl.startsWith('http') ? '' : item.linkUrl"
                :href="item.linkUrl.startsWith('http') ? item.linkUrl : undefined"
                :target="item.openInNewTab ? '_blank' : '_self'"
                class="btn btn-primary px-8 py-3 text-lg"
              >
                了解更多
              </RouterLink>
            </div>
          </div>

        <!-- 轮播控制 -->
        <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20 flex space-x-2">
          <button
            v-for="(_, index) in banners"
            :key="index"
            @click="goToSlide(index)"
            class="w-3 h-3 rounded-full bg-white/50 transition-all"
            :class="index === currentSlide ? 'bg-white w-10' : 'bg-white/50'"
            aria-label="切换轮播图"
          ></button>
        </div>

        <!-- 左右箭头 (仅在桌面端显示) -->
        <button
          @click="prevSlide"
          class="hidden md:flex absolute left-4 top-1/2 transform -translate-y-1/2 z-20 items-center justify-center w-12 h-12 rounded-full bg-black/30 text-white hover:bg-black/50 transition-all"
          aria-label="上一张"
        >
          <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button
          @click="nextSlide"
          class="hidden md:flex absolute right-4 top-1/2 transform -translate-y-1/2 z-20 items-center justify-center w-12 h-12 rounded-full bg-black/30 text-white hover:bg-black/50 transition-all"
          aria-label="下一张"
        >
          <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
        </div>

        <!-- 空状态提示 -->
        <div v-else class="absolute inset-0 flex items-center justify-center bg-gray-800 text-white rounded-lg">
          <p>暂无轮播图数据</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { getPublicBanners, type Banner } from '../api/banners';
import { useRouter } from 'vue-router';

// 数据
const banners = ref<Banner[]>([]);
const currentSlide = ref<number>(0);
const loading = ref<boolean>(true);
const error = ref<string | null>(null);
let timer: NodeJS.Timeout | null = null;
const router = useRouter();

// 加载轮播图数据
const loadBanners = async (): Promise<void> => {
  loading.value = true;
  error.value = null;

  try {
    const response = await getPublicBanners();
    if (response.data && response.data.success && Array.isArray(response.data.data)) {
      banners.value = response.data.data;
      console.log('加载轮播图成功:', banners.value);
    } else {
      console.warn('Banner API返回数据格式异常:', response.data);
      banners.value = [];
    }
  } catch (err) {
    console.error('加载轮播图失败:', err);
    error.value = '加载轮播图数据失败，请稍后重试';
    banners.value = [];
  } finally {
    loading.value = false;
  }
};

// 切换到下一张
const nextSlide = () => {
  if (banners.value.length === 0) return;
  currentSlide.value = (currentSlide.value + 1) % banners.value.length;
};

// 切换到上一张
const prevSlide = () => {
  if (banners.value.length === 0) return;
  currentSlide.value = currentSlide.value === 0 ? banners.value.length - 1 : currentSlide.value - 1;
};

// 切换到指定幻灯片
const goToSlide = (index: number) => {
  currentSlide.value = index;
};

// 处理轮播图点击事件
const handleBannerClick = (item: Banner) => {
  if (item.linkUrl) {
    if (item.linkUrl.startsWith('http')) {
      // 外部链接，新窗口打开
      if (item.openInNewTab) {
        window.open(item.linkUrl, '_blank');
      } else {
        window.location.href = item.linkUrl;
      }
    } else {
      // 内部路由链接，使用路由导航
      router.push(item.linkUrl);
    }
  }
};

// 开始自动轮播
const startAutoSlide = () => {
  stopAutoSlide(); // 先清除可能存在的定时器
  timer = setInterval(nextSlide, 5000); // 5秒自动切换
};

// 停止自动轮播
const stopAutoSlide = () => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
};

onMounted(() => {
  // 加载轮播图数据
  loadBanners();
  
  // 开始自动轮播
  startAutoSlide();
});

onUnmounted(() => {
  // 组件销毁时清除定时器
  stopAutoSlide();
});
</script>

<style scoped>
.bg-primary {
  background-color: #7B5AFA;
}

.bg-primary-dark {
  background-color: #6039E4;
}

.bg-secondary {
  background-color: #6039E4;
}
</style>