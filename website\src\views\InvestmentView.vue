<script setup>
import { ref, computed } from 'vue'

// 投资金额范围
const minInvestment = 100000 // 10万
const maxInvestment = 5000000 // 500万
const investmentStep = 50000 // 5万

// 投资金额及预期收益
const investment = ref(minInvestment)
const investmentYears = ref(1)

// 计算年化收益率
const annualRate = computed(() => {
  // 基础收益率10%
  let rate = 0.1
  
  // 每增加100万，额外增加2%，最高到20%
  const additionalRate = Math.min(Math.floor(investment.value / 1000000) * 0.02, 0.1)
  
  return rate + additionalRate
})

// 计算预期收益
const expectedReturn = computed(() => {
  return investment.value * Math.pow((1 + annualRate.value), investmentYears.value) - investment.value
})

// 格式化数字为金额
const formatCurrency = (value) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

// 计算投资方案等级
const investmentTier = computed(() => {
  if (investment.value >= 3000000) return '钻石级'
  if (investment.value >= 1000000) return '金牌'
  if (investment.value >= 500000) return '银牌'
  return '铜牌'
})

// 投资方案特权
const tierBenefits = computed(() => {
  const benefits = {
    '钻石级': ['专属定制短剧IP', '优先分红权', '制作人署名权', '参与剧本研讨会', '全流程监督权'],
    '金牌': ['前3个项目优先选择权', '季度投资人会议', '项目专属顾问', '定期探班机会'],
    '银牌': ['项目进度实时通报', '半年度投资人会议', '作品首映礼VIP邀请'],
    '铜牌': ['季度财务报告', '年度投资人会议', '成片优先观看权']
  }
  
  return benefits[investmentTier.value]
})

// 常见问题
const faqs = ref([
  {
    question: '投资周期是多久？',
    answer: '标准投资周期为12个月，可选择续投。钻石级和金牌投资人可享受灵活退出机制。'
  },
  {
    question: '如何确保投资安全？',
    answer: '我们建立了完善的风控体系，包括资金专款专用、财务透明公示、定期审计，并提供合同收益保障条款。'
  },
  {
    question: '投资回报如何结算？',
    answer: '根据合同约定，基础收益按季度结算，额外收益在项目结束后30个工作日内一次性结算。'
  },
  {
    question: '能否参与多个项目？',
    answer: '可以，金牌及以上级别投资人可同时参与多个项目，并享受组合投资优惠。'
  }
])
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 投资方案头部 -->
    <section class="bg-gray-50 py-8">
      <div class="container mx-auto px-4">
        <div class="bg-gradient-primary text-white py-12 rounded-lg">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center px-8">
          <div>
            <h1 class="text-4xl md:text-5xl font-bold mb-6">灵活多样的投资方案</h1>
            <p class="text-xl opacity-90 mb-8">
              剧募募为您提供透明、高效、灵活的投资方式，让您的资金创造最大价值。
              根据投资金额，享受不同级别的权益与收益。
            </p>
            <div class="flex space-x-4">
              <a href="#calculator" class="btn bg-white text-primary hover:bg-blue-50">
                试算投资收益
              </a>
              <a href="#plans" class="btn border border-white text-white hover:bg-primary-dark">
                查看投资方案
              </a>
            </div>
          </div>
          
          <div class="hidden lg:flex justify-end">
            <!-- 使用SVG替代图片 -->
            <svg class="max-h-80 text-white" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="20" y="40" width="160" height="120" rx="10" stroke="currentColor" stroke-width="4"/>
              <path d="M40 80L80 120L120 80" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M140 60L160 80L140 100" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="60" cy="60" r="10" stroke="currentColor" stroke-width="4"/>
              <path d="M20 140H180" stroke="currentColor" stroke-width="4"/>
              <path d="M60 140V160" stroke="currentColor" stroke-width="4"/>
              <path d="M100 140V160" stroke="currentColor" stroke-width="4"/>
              <path d="M140 140V160" stroke="currentColor" stroke-width="4"/>
            </svg>
          </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 投资收益计算器 -->
    <section id="calculator" class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">投资收益计算器</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            根据投资金额试算可能的收益，收益率会随投资规模提升而增长
          </p>
        </div>
        
        <div class="max-w-4xl mx-auto">
          <div class="card p-8 shadow-lg">
            <!-- 投资金额滑块 -->
            <div class="mb-8">
              <div class="flex justify-between mb-2">
                <label class="text-lg font-medium">投资金额</label>
                <span class="text-lg font-bold text-primary">{{ formatCurrency(investment) }}</span>
              </div>
              
              <input 
                type="range" 
                v-model.number="investment" 
                :min="minInvestment" 
                :max="maxInvestment" 
                :step="investmentStep" 
                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              >
              
              <div class="flex justify-between mt-1 text-sm text-gray-600">
                <span>{{ formatCurrency(minInvestment) }}</span>
                <span>{{ formatCurrency(maxInvestment) }}</span>
              </div>
            </div>
            
            <!-- 投资年限选择 -->
            <div class="mb-8">
              <div class="flex justify-between mb-2">
                <label class="text-lg font-medium">投资年限</label>
                <span class="text-lg font-bold text-primary">{{ investmentYears }}年</span>
              </div>
              
              <div class="flex space-x-4">
                <button 
                  v-for="year in 3" 
                  :key="year"
                  @click="investmentYears = year" 
                  class="flex-1 py-3 rounded-md border-2 transition-colors"
                  :class="investmentYears === year ? 'border-primary bg-blue-50 text-primary' : 'border-gray-200 hover:border-primary'"
                >
                  {{ year }}年
                </button>
              </div>
            </div>
            
            <!-- 计算结果展示 -->
            <div class="bg-gray-50 p-6 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                  <h3 class="text-gray-500 font-medium">投资等级</h3>
                  <p class="text-2xl font-bold mt-2">{{ investmentTier }}</p>
                </div>
                <div>
                  <h3 class="text-gray-500 font-medium">预期年化收益率</h3>
                  <p class="text-2xl font-bold mt-2 text-green-600">{{ (annualRate.value * 100).toFixed(1) }}%</p>
                </div>
                <div>
                  <h3 class="text-gray-500 font-medium">预期总收益</h3>
                  <p class="text-2xl font-bold mt-2 text-green-600">{{ formatCurrency(expectedReturn) }}</p>
                </div>
              </div>
              
              <!-- 投资按钮 -->
              <div class="mt-8 text-center">
                <button class="btn btn-primary px-8 py-3 text-lg">
                  咨询投资顾问
                </button>
                <p class="text-sm text-gray-500 mt-2">我们的投资顾问将在24小时内与您联系</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 投资方案详情 -->
    <section id="plans" class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">投资特权与权益</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            不同级别的投资人享受差异化的服务与权益，投资越多，回报越丰厚
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- 铜牌 -->
          <div class="card hover:shadow-xl transition-all">
            <div class="bg-amber-100 rounded-t-lg px-6 py-4 border-b border-amber-200">
              <h3 class="text-2xl font-bold text-amber-800">铜牌</h3>
              <p class="text-amber-700">10万起投</p>
            </div>
            
            <div class="p-6">
              <h4 class="text-xl font-semibold mb-4">投资特权</h4>
              <ul class="space-y-3">
                <li v-for="(benefit, index) in tierBenefits['铜牌']" :key="index" class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span>{{ benefit }}</span>
                </li>
              </ul>
              
              <div class="mt-6">
                <p class="text-gray-700">年化收益率</p>
                <p class="text-3xl font-bold text-amber-600 mt-1">10%</p>
              </div>
              
              <button class="w-full btn btn-primary mt-6">选择此方案</button>
            </div>
          </div>
          
          <!-- 银牌 -->
          <div class="card hover:shadow-xl transition-all">
            <div class="bg-gray-100 rounded-t-lg px-6 py-4 border-b border-gray-200">
              <h3 class="text-2xl font-bold text-gray-700">银牌</h3>
              <p class="text-gray-600">50万起投</p>
            </div>
            
            <div class="p-6">
              <h4 class="text-xl font-semibold mb-4">投资特权</h4>
              <ul class="space-y-3">
                <li v-for="(benefit, index) in tierBenefits['银牌']" :key="index" class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span>{{ benefit }}</span>
                </li>
              </ul>
              
              <div class="mt-6">
                <p class="text-gray-700">年化收益率</p>
                <p class="text-3xl font-bold text-gray-600 mt-1">12%</p>
              </div>
              
              <button class="w-full btn btn-primary mt-6">选择此方案</button>
            </div>
          </div>
          
          <!-- 金牌 -->
          <div class="card hover:shadow-xl transition-all ring-2 ring-yellow-500">
            <div class="bg-yellow-100 rounded-t-lg px-6 py-4 border-b border-yellow-200 relative overflow-hidden">
              <div class="absolute top-0 right-0">
                <div class="bg-yellow-500 text-white text-xs px-4 py-1 transform rotate-45 translate-x-1/2 translate-y-1">
                  推荐
                </div>
              </div>
              <h3 class="text-2xl font-bold text-yellow-800">金牌</h3>
              <p class="text-yellow-700">100万起投</p>
            </div>
            
            <div class="p-6">
              <h4 class="text-xl font-semibold mb-4">投资特权</h4>
              <ul class="space-y-3">
                <li v-for="(benefit, index) in tierBenefits['金牌']" :key="index" class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span>{{ benefit }}</span>
                </li>
              </ul>
              
              <div class="mt-6">
                <p class="text-gray-700">年化收益率</p>
                <p class="text-3xl font-bold text-yellow-600 mt-1">16%</p>
              </div>
              
              <button class="w-full btn btn-primary mt-6">选择此方案</button>
            </div>
          </div>
          
          <!-- 钻石级 -->
          <div class="card hover:shadow-xl transition-all">
            <div class="bg-blue-100 rounded-t-lg px-6 py-4 border-b border-blue-200">
              <h3 class="text-2xl font-bold text-blue-800">钻石级</h3>
              <p class="text-blue-700">300万起投</p>
            </div>
            
            <div class="p-6">
              <h4 class="text-xl font-semibold mb-4">投资特权</h4>
              <ul class="space-y-3">
                <li v-for="(benefit, index) in tierBenefits['钻石级']" :key="index" class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span>{{ benefit }}</span>
                </li>
              </ul>
              
              <div class="mt-6">
                <p class="text-gray-700">年化收益率</p>
                <p class="text-3xl font-bold text-blue-600 mt-1">20%</p>
              </div>
              
              <button class="w-full btn btn-primary mt-6">选择此方案</button>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 常见问题 -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">常见问题</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            关于投资流程、收益结算、风险管控等问题的解答
          </p>
        </div>
        
        <div class="max-w-3xl mx-auto">
          <div v-for="(faq, index) in faqs" :key="index" class="mb-6">
            <div class="flex items-start">
              <div class="bg-blue-100 text-blue-600 rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                <span class="font-bold">Q</span>
              </div>
              <h3 class="text-xl font-semibold">{{ faq.question }}</h3>
            </div>
            <div class="ml-12 mt-2 text-gray-600">
              {{ faq.answer }}
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 投资咨询 -->
    <section class="bg-gradient-primary text-white py-12">
      <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row items-center justify-between">
          <div>
            <h2 class="text-2xl md:text-3xl font-bold mb-2">准备好开始您的投资了吗？</h2>
            <p class="opacity-90">我们的投资顾问随时为您提供专业咨询服务</p>
          </div>
          <div class="mt-6 md:mt-0">
            <button class="btn bg-white text-primary hover:bg-blue-50 px-8 py-3">
              立即咨询
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template> 