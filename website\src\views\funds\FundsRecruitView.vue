<script setup>
import { ref, computed, reactive, onMounted, watch } from 'vue'
import { RouterLink } from 'vue-router'
import { getPublicFunds, getFundTypes, getRiskLevels, getPeriodTypes } from '../../api/public/fundApi'
import { formatCurrency, calculateProgress } from '../../api/utils'

// 加载状态
const loading = ref(false)
const error = ref(null)

// 数据状态
const funds = ref([])
const filterOptions = reactive({
  type: [],
  risk: [],
  period: []
})

// 筛选条件
const filters = reactive({
  type: [], // 基金类型：股权型/债权型/混合型
  risk: [], // 风险等级：R1~R5
  minInvestment: 0, // 起投金额：最小值
  period: [] // 封闭期：≤1年 / 1-3年 / >3年
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 已选中的基金进行对比
const selectedForComparison = ref([])

// 初始化筛选条件选项
const initFilterOptions = async () => {
  try {
    // 这里使用Promise.all同时加载所有筛选选项
    const [types, risks, periods] = await Promise.all([
      getFundTypes(),
      getRiskLevels(),
      getPeriodTypes()
    ])
    
    filterOptions.type = types
    filterOptions.risk = risks
    filterOptions.period = periods
  } catch (err) {
    console.error('加载筛选选项失败:', err)
    error.value = '加载筛选选项失败，请刷新页面重试'
  }
}

// 加载基金数据
const loadFunds = async () => {
  loading.value = true
  error.value = null
  
  try {
    // 构建筛选参数
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    // 添加类型筛选
    if (filters.type && filters.type.length > 0) {
      params.type = filters.type
    }
    
    // 添加风险筛选
    if (filters.risk && filters.risk.length > 0) {
      params.risk = filters.risk
    }
    
    // 添加封闭期筛选
    if (filters.period && filters.period.length > 0) {
      params.period = filters.period
    }
    
    // 添加起投金额筛选
    if (filters.minInvestment > 0) {
      params.minInvestment = filters.minInvestment
    }
    
    // 获取数据
    const response = await getPublicFunds(params)
    funds.value = response.data.data.list

    console.log('筛选后的基金数据:', funds.value)

    // 更新分页信息
    if (response.data.data.pagination) {
      pagination.total = response.data.data.pagination.total
    }
  } catch (err) {
    console.error('加载基金数据失败:', err)
    error.value = '加载基金数据失败，请刷新页面重试'
  } finally {
    loading.value = false
  }
}

// 根据筛选条件过滤基金
const filteredFunds = computed(() => {
  return funds.value
})

// 切换基金对比选择
const toggleFundSelection = (fundId) => {
  const index = selectedForComparison.value.indexOf(fundId)
  if (index === -1) {
    // 如果已经选择了4个，不能再添加
    if (selectedForComparison.value.length >= 4) {
      return
    }
    selectedForComparison.value.push(fundId)
  } else {
    selectedForComparison.value.splice(index, 1)
  }
}

// 监听筛选条件变化，自动加载数据
watch(() => [...filters.type], () => {
  console.log('基金类型筛选变化，自动重新加载数据')
  pagination.page = 1
  loadFunds()
}, { deep: true })

watch(() => [...filters.risk], () => {
  console.log('风险等级筛选变化，自动重新加载数据')
  pagination.page = 1
  loadFunds()
}, { deep: true })

watch(() => [...filters.period], () => {
  console.log('封闭期筛选变化，自动重新加载数据')
  pagination.page = 1
  loadFunds()
}, { deep: true })

watch(() => filters.minInvestment, (newVal, oldVal) => {
  // 添加防抖，避免滑动条拖动过程中频繁请求
  if (minInvestmentTimer) clearTimeout(minInvestmentTimer)
  minInvestmentTimer = setTimeout(() => {
    console.log('起投金额筛选变化，自动重新加载数据', newVal)
    pagination.page = 1
    loadFunds()
  }, 500) // 500ms防抖
})

// 用于起投金额滑动条的防抖
let minInvestmentTimer = null

// 清空筛选条件
const clearFilters = () => {
  console.log('清空筛选条件')
  filters.type = []
  filters.risk = []
  filters.minInvestment = 0
  filters.period = []
  
  // 重新加载数据
  pagination.page = 1
  loadFunds()
}

// 收藏基金
const toggleFavorite = (fundId) => {
  const fund = funds.value.find(f => f.id === fundId)
  if (fund) {
    fund.isFavorite = !fund.isFavorite
  }
}

// 获取风险等级对应的颜色
const getRiskLevelColor = (risk) => {
  const riskItem = filterOptions.risk.find(r => r.id === risk)
  return riskItem ? riskItem.color : 'bg-gray-500'
}

// 获取封闭期文本
const getPeriodText = (periodId) => {
  const period = filterOptions.period.find(p => p.id === periodId)
  return period ? period.label : ''
}

// 页面加载
onMounted(async () => {
  await initFilterOptions()
  loadFunds()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="container mx-auto px-4">
      <!-- 页面标题 -->
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 class="text-2xl md:text-3xl font-bold">基金招募</h1>
          <p class="text-gray-600 mt-2">探索我们精选的文旅产业基金产品</p>
        </div>
        <div class="mt-4 md:mt-0">
          <RouterLink to="/funds" class="text-primary flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12" />
            </svg>
            返回基金主页
          </RouterLink>
        </div>
      </div>
      
      <!-- 筛选器组件 -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div class="flex flex-wrap justify-between items-center mb-5">
          <h2 class="text-xl font-bold">筛选条件</h2>
          <button 
            @click="clearFilters" 
            class="text-gray-500 hover:text-primary text-sm flex items-center"
          >
            <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            清空筛选
          </button>
        </div>
        
        <!-- 基金类型筛选 -->
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-700 mb-3">基金类型</h3>
          <div class="flex flex-wrap gap-2">
            <label 
              v-for="option in filterOptions.type" 
              :key="option.id"
              class="inline-flex items-center py-1.5 px-3 rounded-full border cursor-pointer transition-colors"
              :class="filters.type.includes(option.id) 
                ? 'bg-primary text-white border-primary' 
                : 'bg-white text-gray-700 border-gray-200 hover:border-gray-300'"
            >
              <input 
                type="checkbox" 
                :value="option.id" 
                v-model="filters.type" 
                class="hidden"
              >
              <span>{{ option.label }}</span>
            </label>
          </div>
        </div>
        
        <!-- 风险等级筛选 -->
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-700 mb-3">风险等级</h3>
          <div class="flex flex-wrap gap-2">
            <label 
              v-for="option in filterOptions.risk" 
              :key="option.id"
              class="inline-flex items-center py-1.5 px-3 rounded-full border cursor-pointer transition-colors"
              :class="filters.risk.includes(option.id)
                ? 'bg-primary text-white border-primary'
                : 'bg-white text-gray-700 border-gray-200 hover:border-gray-300'"
            >
              <input 
                type="checkbox" 
                :value="option.id" 
                v-model="filters.risk" 
                class="hidden"
              >
              <span>{{ option.id }} ({{ option.label }})</span>
            </label>
          </div>
        </div>
        
        <!-- 起投金额筛选 -->
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-700 mb-3">起投金额</h3>
          <div class="flex items-center">
            <span class="text-sm text-gray-600 mr-2">0</span>
            <div class="flex-1">
              <input 
                type="range" 
                v-model.number="filters.minInvestment" 
                min="0" 
                max="5000000" 
                step="100000" 
                class="w-full accent-primary"
              >
            </div>
            <span class="text-sm text-gray-600 ml-2">500万</span>
          </div>
          <div class="text-right text-sm text-gray-600 mt-1">
            当前: {{ formatCurrency(filters.minInvestment) }}元
          </div>
        </div>
        
        <!-- 封闭期筛选 -->
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-700 mb-3">封闭期</h3>
          <div class="flex flex-wrap gap-2">
            <label 
              v-for="option in filterOptions.period" 
              :key="option.id"
              class="inline-flex items-center py-1.5 px-3 rounded-full border cursor-pointer transition-colors"
              :class="filters.period.includes(option.id)
                ? 'bg-primary text-white border-primary'
                : 'bg-white text-gray-700 border-gray-200 hover:border-gray-300'"
            >
              <input 
                type="checkbox" 
                :value="option.id" 
                v-model="filters.period" 
                class="hidden"
              >
              <span>{{ option.label }}</span>
            </label>
          </div>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="bg-white rounded-xl shadow-md p-12 mb-8 flex justify-center">
        <div class="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
      </div>
      
      <!-- 错误信息 -->
      <div v-else-if="error" class="bg-white rounded-xl shadow-md p-8 mb-8 text-center">
        <svg class="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        <p class="text-xl font-medium text-gray-900 mb-2">出错了</p>
        <p class="text-gray-600">{{ error }}</p>
        <button @click="loadFunds" class="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors">
          重试
        </button>
      </div>
      
      <!-- 基金列表 -->
      <div v-else-if="filteredFunds.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div 
          v-for="fund in filteredFunds" 
          :key="fund.id" 
          class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
        >
          <!-- 基金头部 -->
          <div class="p-5">
            <div class="flex justify-between items-start mb-2">
              <h3 class="text-lg font-bold">{{ fund.title }}</h3>
              <div class="flex items-center space-x-2">
                <span class="px-3 py-1 text-sm rounded text-white font-medium" :class="getRiskLevelColor(fund.risk)">{{ fund.risk }}</span>
                <button @click="toggleFavorite(fund.id)" class="text-gray-400 hover:text-yellow-500">
                  <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" :class="{ 'text-yellow-500 fill-yellow-500': fund.isFavorite }">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                </button>
              </div>
            </div>
            <div class="text-gray-500 text-sm mb-5">{{ fund.id }}</div>
            
            <!-- 募集进度 -->
            <div class="mb-5">
              <div class="flex justify-between text-sm mb-1">
                <span>已募集</span>
                <span>{{ calculateProgress(fund.raisedAmount, fund.targetSize) }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                <div 
                  class="bg-indigo-500 h-2.5 rounded-full" 
                  :style="{ width: calculateProgress(fund.raisedAmount, fund.targetSize) + '%' }"
                ></div>
              </div>
              <div class="flex justify-between text-sm">
                <span>{{ formatCurrency(fund.raisedAmount) }}</span>
                <span class="text-gray-500">目标 {{ formatCurrency(fund.targetSize) }}</span>
              </div>
            </div>
            
            <!-- 基金信息 -->
            <div class="space-y-2 mb-5">
              <div class="flex justify-between">
                <div class="text-gray-500">预期年化</div>
                <div class="font-medium">{{ fund.expectedReturn }}</div>
              </div>
              <div class="flex justify-between">
                <div class="text-gray-500">最低起投</div>
                <div class="font-medium">{{ formatCurrency(fund.minInvestment) }}元</div>
              </div>
              <div class="flex justify-between">
                <div class="text-gray-500">最低持有期</div>
                <div class="font-medium">{{ fund.minHoldingPeriod || 24 }}个月</div>
              </div>
              <div class="flex justify-between">
                <div class="text-gray-500">封闭期</div>
                <div class="font-medium">{{ getPeriodText(fund.period) }}</div>
              </div>
            </div>
            
            <!-- 风险提示和操作 -->
            <div class="border-t border-gray-100 mt-4 pt-4 flex items-center justify-between">
              <div class="flex items-center text-yellow-600 text-sm">
                <svg class="w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
                <span>风险提示 (点击查看)</span>
              </div>
            </div>

            <!-- 按钮操作区 -->
            <div class="border-t border-gray-100 mt-2 pt-4 flex items-center justify-between">
              <label class="inline-flex items-center text-gray-600 cursor-pointer">
                <input 
                  type="checkbox" 
                  :checked="selectedForComparison.includes(fund.id)" 
                  @change="toggleFundSelection(fund.id)" 
                  class="form-checkbox h-4 w-4 text-primary rounded"
                >
                <span class="ml-2 text-sm">加入对比</span>
              </label>
              <RouterLink 
                :to="`/funds/${fund.id}`" 
                class="inline-block px-10 py-2 border rounded text-primary border-primary hover:bg-primary hover:text-white focus:bg-primary focus:text-white active:bg-primary active:text-white transition-colors text-sm font-medium"
              >
                查看详情
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="bg-white rounded-xl shadow-md p-12 mb-8 text-center">
        <svg class="w-20 h-20 text-gray-400 mx-auto mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p class="text-xl font-medium text-gray-900 mb-2">暂无符合条件的基金</p>
        <p class="text-gray-600 mb-4">请尝试调整筛选条件后重新查询</p>
        <button @click="clearFilters" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors">
          清空筛选条件
        </button>
      </div>
      
      <!-- 分页区域 -->
      <div v-if="!loading && !error && filteredFunds.length > 0" class="flex justify-center my-8">
        <div class="flex space-x-2">
          <button 
            @click="pagination.page > 1 && (pagination.page--, loadFunds())" 
            class="px-3 py-1 rounded border"
            :class="pagination.page > 1 ? 'text-primary border-primary' : 'text-gray-400 border-gray-200 cursor-not-allowed'"
          >
            上一页
          </button>
          <div class="px-3 py-1 text-gray-700">
            第 {{ pagination.page }} 页 / 共 {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
          </div>
          <button
            @click="pagination.page < Math.ceil(pagination.total / pagination.pageSize) && (pagination.page++, loadFunds())"
            class="px-3 py-1 rounded border"
            :class="pagination.page < Math.ceil(pagination.total / pagination.pageSize) ? 'text-primary border-primary' : 'text-gray-400 border-gray-200 cursor-not-allowed'"
          >
            下一页
          </button>
        </div>
      </div>
      
      <!-- 对比浮层 -->
      <div 
        v-if="selectedForComparison.length > 0" 
        class="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 p-4 z-50"
      >
        <div class="container mx-auto">
          <div class="flex justify-between items-center">
            <div class="flex items-center">
              <span class="font-medium mr-4">已选择 {{ selectedForComparison.length }} 个基金进行对比</span>
              <div class="flex space-x-3">
                <div 
                  v-for="fundId in selectedForComparison" 
                  :key="fundId" 
                  class="flex items-center bg-gray-100 rounded-full px-3 py-1"
                >
                  <span class="text-sm truncate max-w-[120px]">{{ funds.value.find(f => f.id === fundId)?.title }}</span>
                  <button @click="toggleFundSelection(fundId)" class="ml-1 text-gray-500">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <div class="flex space-x-4">
              <button 
                @click="selectedForComparison = []" 
                class="text-gray-500 hover:text-gray-700"
              >
                清空
              </button>
              <RouterLink 
                :to="`/funds/compare?ids=${selectedForComparison.join(',')}`" 
                class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                开始对比
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.text-primary {
  color: #8667F0;
}
.bg-primary {
  background-color: #8667F0;
}
.border-primary {
  border-color: #8667F0;
}
.bg-primary-dark {
  background-color: #6039E4;
}
.hover\:bg-primary-dark:hover {
  background-color: #6039E4;
}
.accent-primary {
  accent-color: #8667F0;
}
</style> 