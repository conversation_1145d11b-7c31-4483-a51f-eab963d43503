<script setup>
import { ref, onMounted } from 'vue'
import { getPublicActors } from '../api/actors'

// 演员数据
const actors = ref([])
const visibleActors = ref([])
const showAllActors = ref(false)

// 获取演员数据
const loadActors = async () => {
  try {
    const response = await getPublicActors()
    if (response.data.success) {
      actors.value = response.data.data.map(actor => {
        // 安全地解析JSON字符串，添加默认值和非空检查
        let specialty = [];
        let experience = [];
        let description = '';
        let gender = '';
        let age = '';
        let height = '';
        let weight = '';
        
        try {
          // 解析标签 - 不使用JSON.parse，直接使用字符串分割
          if (actor.tags) {
            specialty = actor.tags.split(',').map(tag => tag.trim());
          }
          
          // 解析简介
          if (actor.bio) {
            let bioObj = {};
            if (typeof actor.bio === 'string') {
              try {
                bioObj = JSON.parse(actor.bio);
              } catch (e) {
                console.error('JSON解析失败:', e);
                bioObj = { description: actor.bio };
              }
            } else if (typeof actor.bio === 'object') {
              bioObj = actor.bio;
            }
            
            experience = Array.isArray(bioObj.experience) ? bioObj.experience : [];
            description = bioObj.description || '';
            gender = bioObj.gender || '';
            age = bioObj.age || '';
            height = bioObj.height || '';
            weight = bioObj.weight || '';
          }
        } catch (e) {
          console.error('解析演员数据失败:', e);
        }
        
        return {
          ...actor,
          specialty,
          experience,
          description,
          gender,
          age,
          height,
          weight
        };
      })
      
      // 默认只显示前10个艺人(2行5列)
      updateVisibleActors()
    }
  } catch (error) {
    console.error('获取演员数据失败', error)
  }
}

// 更新可见艺人列表
const updateVisibleActors = () => {
  if (showAllActors.value) {
    visibleActors.value = actors.value
  } else {
    visibleActors.value = actors.value.slice(0, 10) // 显示前10个(2行5列)
  }
}

// 切换显示所有艺人
const toggleShowAllActors = () => {
  showAllActors.value = !showAllActors.value
  updateVisibleActors()
}

// 页面加载时获取数据
onMounted(() => {
  loadActors()
})

// 服务项目
const services = ref([
  {
    title: '艺人经纪',
    icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z',
    description: '为签约艺人提供全方位的经纪服务，包括演艺资源开发、演出安排、行程规划、形象包装等。'
  },
  {
    title: '内容制作',
    icon: 'M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z',
    description: '策划制作符合艺人特质的内容作品，包括网剧、短视频、音乐作品、直播等多种形式。'
  },
  {
    title: '商业合作',
    icon: 'M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
    description: '开发和执行艺人的商业价值，包括品牌代言、商业演出、直播带货、粉丝经济等。'
  },
  {
    title: '培训提升',
    icon: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
    description: '为艺人提供专业的培训课程，包括表演技巧、镜头表现、台词训练、形体训练等。'
  }
])

// 合作品牌
const brands = ref([
  { name: '爱奇艺', logo: '/images/brands/iqiyi.png' },
  { name: '腾讯视频', logo: '/images/brands/tencent.png' },
  { name: '优酷', logo: '/images/brands/youku.png' },
  { name: '抖音', logo: '/images/brands/douyin.png' },
  { name: '快手', logo: '/images/brands/kuaishou.png' },
  { name: '华为', logo: '/images/brands/huawei.png' },
  { name: '耐克', logo: '/images/brands/nike.png' }
])

// 当前选中的演员
const selectedActor = ref(null)

// 查看演员详情
const viewActorDetail = (actor) => {
  selectedActor.value = actor
}

// 关闭演员详情
const closeActorDetail = () => {
  selectedActor.value = null
}
</script>

<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <section class="bg-gray-50 py-4">
      <div class="container mx-auto px-4">
        <div class="bg-gradient-primary text-white py-12 rounded-lg text-center">
          <h1 class="text-4xl md:text-5xl font-bold mb-4">演艺经纪</h1>
          <p class="text-xl max-w-3xl mx-auto opacity-90">
            剧募募拥有专业的演艺经纪团队，致力于发掘和培养有潜力的影视人才，
            为创作优质内容提供坚实的人才基础
          </p>
        </div>
      </div>
    </section>
    
    <!-- 我们的服务 -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gradient">我们的服务</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            为艺人提供全方位的经纪管理与职业发展规划
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div
            v-for="service in services"
            :key="service.title"
            class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300"
          >
            <div class="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center text-white mb-6">
              <svg class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="service.icon" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-3">{{ service.title }}</h3>
            <p class="text-gray-600">{{ service.description }}</p>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 艺人展示 -->
    <section v-if="actors.length > 0" class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gradient">签约艺人</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            我们拥有多位优秀签约艺人，活跃于各大影视剧、综艺和商业活动
          </p>
        </div>
        
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6">
          <div
            v-for="actor in visibleActors"
            :key="actor.id"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
          >
            <!-- 演员头像 -->
            <div class="h-64 bg-gray-200 flex items-center justify-center">
              <img 
                v-if="actor.avatar_url" 
                :src="actor.avatar_url" 
                :alt="actor.name"
                class="w-full h-full object-cover"
              >
              <svg 
                v-else
                class="w-24 h-24 text-gray-400" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            
            <div class="p-4">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-lg font-bold">{{ actor.name }}</h3>
                <div class="text-xs text-gray-500">{{ actor.gender }} | {{ actor.age }}岁</div>
              </div>
              
              <div class="mb-3">
                <div class="text-xs text-gray-600 mb-1">代表作品：</div>
                <p class="text-primary font-medium text-sm truncate">{{ actor.experience && actor.experience.length > 0 ? actor.experience[0] : '暂无' }}</p>
              </div>
              
              <div class="flex flex-wrap gap-1 mb-3">
                <span 
                  v-for="(skill, index) in actor.specialty" 
                  :key="index"
                  class="px-2 py-1 bg-blue-50 text-primary text-xs rounded-full"
                >
                  {{ skill }}
                </span>
              </div>
              
              <button
                @click="viewActorDetail(actor)"
                class="w-full py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors text-sm"
              >
                查看详情
              </button>
            </div>
          </div>
        </div>
        
        <div v-if="actors.length > 10" class="mt-8 text-center">
          <button 
            @click="toggleShowAllActors" 
            class="inline-flex items-center px-6 py-3 border border-primary text-primary bg-white hover:bg-primary hover:text-white rounded-lg transition-colors"
          >
            <span>{{ showAllActors ? '收起' : '更多艺人' }}</span>
            <svg 
              class="w-5 h-5 ml-2" 
              :class="{'transform rotate-180': showAllActors}"
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>
    </section>
    
    <!-- 合作品牌 -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gradient">合作品牌</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            我们的艺人与多家知名品牌建立了长期稳定的合作关系
          </p>
        </div>
        
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6">
          <div
            v-for="brand in brands"
            :key="brand.name"
            class="bg-white rounded-lg shadow-sm p-6 flex items-center justify-center h-32"
          >
            <!-- 临时占位图像 -->
            <div class="text-xl font-medium text-gray-400">{{ brand.name }}</div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 联系合作 -->
    <section class="py-16 bg-gradient-primary text-white">
      <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl font-bold mb-6">寻找新星 · 合作共赢</h2>
        <p class="text-xl mb-8 max-w-3xl mx-auto">
          如果您是有潜力的演艺新人或寻求艺人合作的品牌方，欢迎与我们联系
        </p>
        <div class="flex flex-col sm:flex-row justify-center gap-4">
          <button class="btn bg-white text-primary hover:bg-gray-100 px-8 py-3 rounded-lg transition-colors">
            艺人报名
          </button>
          <button class="btn bg-transparent border-2 border-white hover:bg-white/10 px-8 py-3 rounded-lg transition-colors">
            商务合作
          </button>
        </div>
      </div>
    </section>
    
    <!-- 演员详情弹窗 -->
    <div
      v-if="selectedActor"
      class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 p-4"
      @click="closeActorDetail"
    >
      <div
        class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        @click.stop
      >
        <div class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-2xl font-bold">{{ selectedActor.name }}</h3>
            <button
              @click="closeActorDetail"
              class="text-gray-500 hover:text-gray-700"
            >
              <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <!-- 演员头像 -->
              <div class="h-80 bg-gray-200 flex items-center justify-center rounded-lg">
                <img 
                  v-if="selectedActor.avatar_url" 
                  :src="selectedActor.avatar_url" 
                  :alt="selectedActor.name"
                  class="w-full h-full object-cover rounded-lg"
                >
                <svg 
                  v-else
                  class="w-24 h-24 text-gray-400" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
            
            <div>
              <div class="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <div class="text-sm text-gray-500 mb-1">性别</div>
                  <div class="font-medium">{{ selectedActor.gender }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500 mb-1">年龄</div>
                  <div class="font-medium">{{ selectedActor.age }}岁</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500 mb-1">身高</div>
                  <div class="font-medium">{{ selectedActor.height }}cm</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500 mb-1">体重</div>
                  <div class="font-medium">{{ selectedActor.weight }}kg</div>
                </div>
              </div>
              
              <div class="mb-6">
                <div class="text-sm text-gray-500 mb-2">特长</div>
                <div class="flex flex-wrap gap-2">
                  <span
                    v-for="(skill, index) in selectedActor.specialty"
                    :key="index"
                    class="px-3 py-1 bg-blue-50 text-primary text-sm rounded-full"
                  >
                    {{ skill }}
                  </span>
                </div>
              </div>
              
              <div class="mb-6">
                <div class="text-sm text-gray-500 mb-2">代表作品</div>
                <ul class="space-y-2">
                  <li
                    v-for="(exp, index) in selectedActor.experience"
                    :key="index"
                    class="flex items-start"
                  >
                    <span class="text-primary mr-2">•</span>
                    <span>{{ exp }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="mt-6">
            <div class="text-sm text-gray-500 mb-2">艺人简介</div>
            <p class="text-gray-700">{{ selectedActor.description }}</p>
          </div>
          
          <div class="mt-8 flex justify-center">
            <button class="btn bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-lg transition-colors">
              联系经纪人
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg-gradient-primary {
  background: linear-gradient(45deg, var(--color-purple-gradient-start) 50%, var(--color-purple-gradient-end) 100%);
}

.text-gradient {
  background: linear-gradient(to right, var(--color-purple-gradient-start), var(--color-purple-gradient-end));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}
</style> 